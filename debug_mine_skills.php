<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ДИАГНОСТИКА СИСТЕМЫ СКИЛЛОВ В РУДНИКАХ ===\n\n";

// Проверяем шаблоны скиллов
echo "1. ШАБЛОНЫ СКИЛЛОВ МОБОВ:\n";
$templates = \App\Models\MobSkillTemplate::all();
if ($templates->count() > 0) {
    foreach ($templates as $template) {
        echo "   - {$template->name}\n";
        echo "     Шанс: {$template->chance}%\n";
        echo "     Кулдаун: {$template->cooldown}с\n";
        echo "     Длительность: {$template->duration}с\n";
        echo "     Тип эффекта: {$template->effect_type}\n";
        echo "     Активен: " . ($template->is_active ? 'Да' : 'Нет') . "\n";
        echo "     Приоритет: {$template->priority}\n\n";
    }
} else {
    echo "   Нет шаблонов скиллов!\n\n";
}

// Проверяем привязки скиллов к мобам
echo "2. ПРИВЯЗКИ СКИЛЛОВ К МОБАМ:\n";
$mobSkills = \App\Models\MobSkill::with(['mob', 'skillTemplate'])->get();
if ($mobSkills->count() > 0) {
    foreach ($mobSkills as $mobSkill) {
        $mobName = $mobSkill->mob ? $mobSkill->mob->name : 'Неизвестный моб';
        $skillName = $mobSkill->skillTemplate ? $mobSkill->skillTemplate->name : 'Неизвестный скилл';
        echo "   - Моб: {$mobName}\n";
        echo "     Скилл: {$skillName}\n";
        echo "     Шанс: {$mobSkill->chance}%\n";
        echo "     Кулдаун заканчивается: " . ($mobSkill->cooldown_ends_at ? $mobSkill->cooldown_ends_at : 'Нет') . "\n";
        echo "     Последнее использование: " . ($mobSkill->last_used_at ? $mobSkill->last_used_at : 'Никогда') . "\n\n";
    }
} else {
    echo "   Нет привязок скиллов к мобам!\n\n";
}

// Проверяем мобов в рудниках
echo "3. МОБЫ В РУДНИКАХ:\n";
$mineMobs = \App\Models\Mob::where('mob_type', 'mine')
    ->orWhereNotNull('mine_location_id')
    ->with('skills.skillTemplate')
    ->get();

if ($mineMobs->count() > 0) {
    foreach ($mineMobs as $mob) {
        echo "   - {$mob->name} (ID: {$mob->id})\n";
        echo "     Локация: {$mob->location}\n";
        echo "     Mine Location ID: {$mob->mine_location_id}\n";
        echo "     HP: {$mob->hp}/{$mob->max_hp}\n";
        echo "     Скиллов: {$mob->skills->count()}\n";
        
        if ($mob->skills->count() > 0) {
            foreach ($mob->skills as $skill) {
                $templateName = $skill->skillTemplate ? $skill->skillTemplate->name : 'Нет шаблона';
                echo "       * {$templateName} (шанс: {$skill->chance}%)\n";
            }
        }
        echo "\n";
    }
} else {
    echo "   Нет мобов в рудниках!\n\n";
}

// Проверяем активные эффекты обнаружения
echo "4. АКТИВНЫЕ ЭФФЕКТЫ ОБНАРУЖЕНИЯ:\n";
$detectionEffects = \App\Models\ActiveEffect::where('effect_type', 'mine_detection')
    ->orWhere('effect_type', 'mine_detection_debuff')
    ->with('target')
    ->get();

if ($detectionEffects->count() > 0) {
    foreach ($detectionEffects as $effect) {
        $targetName = $effect->target ? $effect->target->name : 'Неизвестная цель';
        echo "   - Цель: {$targetName}\n";
        echo "     Тип эффекта: {$effect->effect_type}\n";
        echo "     Заканчивается: {$effect->ends_at}\n";
        echo "     Активен: " . ($effect->ends_at > now() ? 'Да' : 'Нет') . "\n\n";
    }
} else {
    echo "   Нет активных эффектов обнаружения!\n\n";
}

// Проверяем метки рудников
echo "5. МЕТКИ РУДНИКОВ:\n";
if (\Illuminate\Support\Facades\Schema::hasTable('mine_marks')) {
    $mineMarks = \App\Models\MineMark::with('player')->get();
    if ($mineMarks->count() > 0) {
        foreach ($mineMarks as $mark) {
            $playerName = $mark->player ? $mark->player->name : 'Неизвестный игрок';
            echo "   - Игрок: {$playerName}\n";
            echo "     Рудник ID: {$mark->mine_location_id}\n";
            echo "     Истекает: {$mark->expires_at}\n";
            echo "     Активна: " . ($mark->is_active ? 'Да' : 'Нет') . "\n";
            echo "     Атак: {$mark->attack_count}\n\n";
        }
    } else {
        echo "   Нет активных меток рудников!\n\n";
    }
} else {
    echo "   Таблица mine_marks не существует!\n\n";
}

echo "=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";
