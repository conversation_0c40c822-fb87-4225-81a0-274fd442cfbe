<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($mobSkill->name); ?> - Скилл моба - Админ панель</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="bg-[#1a1814] text-[#d4cbb0] min-h-screen">
    <div class="container mx-auto px-4 py-8">
        
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-[#e4d7b0] mb-2">⚔️ <?php echo e($mobSkill->name); ?></h1>
                    <p class="text-[#998d66]">Детальная информация о скилле моба</p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('admin.mob-skills.index')); ?>" 
                       class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] hover:from-[#3b3629] hover:to-[#2a2722] text-[#d4cbb0] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ← Назад к списку
                    </a>
                    <a href="<?php echo e(route('admin.mob-skills.edit', $mobSkill)); ?>" 
                       class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ✏️ Редактировать
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <div class="lg:col-span-2">
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h2 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-2xl mr-3">📋</span>
                            Основная информация
                        </h2>
                    </div>
                    <div class="p-6 space-y-6">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Название скилла</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0]">
                                    <?php echo e($mobSkill->name); ?>

                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Иконка</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] flex items-center">
                                    <?php if($mobSkill->icon): ?>
                                        <img src="<?php echo e(asset($mobSkill->icon)); ?>" alt="<?php echo e($mobSkill->name); ?>" class="w-8 h-8 mr-3">
                                        <span class="text-sm text-[#998d66]"><?php echo e($mobSkill->icon); ?></span>
                                    <?php else: ?>
                                        <span class="text-[#998d66]">Не указана</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-[#c1a96e] mb-2">Описание</label>
                            <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] min-h-[80px]">
                                <?php echo e($mobSkill->description ?? 'Описание не указано'); ?>

                            </div>
                        </div>

                        
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Тип эффекта</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center">
                                    <span class="inline-block px-3 py-1 rounded-full text-sm font-medium
                                        <?php if($mobSkill->effect_type === 'stun'): ?> bg-red-900/30 text-red-300 border border-red-700
                                        <?php elseif($mobSkill->effect_type === 'buff'): ?> bg-green-900/30 text-green-300 border border-green-700
                                        <?php elseif($mobSkill->effect_type === 'debuff'): ?> bg-orange-900/30 text-orange-300 border border-orange-700
                                        <?php elseif($mobSkill->effect_type === 'dot'): ?> bg-purple-900/30 text-purple-300 border border-purple-700
                                        <?php elseif($mobSkill->effect_type === 'damage'): ?> bg-red-900/30 text-red-300 border border-red-700
                                        <?php else: ?> bg-gray-900/30 text-gray-300 border border-gray-700
                                        <?php endif; ?>">
                                        <?php echo e(ucfirst($mobSkill->effect_type)); ?>

                                    </span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Шанс (%)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    <?php echo e($mobSkill->chance); ?>%
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Кулдаун (сек)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    <?php echo e($mobSkill->cooldown); ?>

                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Длительность (сек)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    <?php echo e($mobSkill->duration); ?>

                                </div>
                            </div>
                        </div>

                        
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Тип цели</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    <?php echo e(ucfirst($mobSkill->target_type)); ?>

                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Мин. HP (%)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    <?php echo e($mobSkill->min_health_percent); ?>%
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Макс. HP (%)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    <?php echo e($mobSkill->max_health_percent); ?>%
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Приоритет</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    <?php echo e($mobSkill->priority); ?>

                                </div>
                            </div>
                        </div>

                        
                        <?php if($mobSkill->effect_data): ?>
                        <div>
                            <label class="block text-sm font-medium text-[#c1a96e] mb-2">Данные эффекта</label>
                            <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3">
                                <pre class="text-sm text-[#d4cbb0] whitespace-pre-wrap"><?php echo e(json_encode($mobSkill->effect_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                            </div>
                        </div>
                        <?php endif; ?>

                        
                        <div class="flex items-center justify-between pt-4 border-t border-[#3b3629]">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-[#c1a96e] mr-3">Статус:</span>
                                <span class="inline-block px-3 py-1 rounded-full text-sm font-medium
                                    <?php if($mobSkill->is_active): ?> bg-green-900/30 text-green-300 border border-green-700
                                    <?php else: ?> bg-red-900/30 text-red-300 border border-red-700
                                    <?php endif; ?>">
                                    <?php echo e($mobSkill->is_active ? 'Активен' : 'Неактивен'); ?>

                                </span>
                            </div>
                            <div class="text-sm text-[#998d66]">
                                Создан: <?php echo e($mobSkill->created_at->format('d.m.Y H:i')); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            
            <div class="space-y-6">
                
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h3 class="text-lg font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-xl mr-3">📊</span>
                            Статистика
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Мобов с этим скиллом:</span>
                            <span class="text-[#c1a96e] font-semibold"><?php echo e($mobsWithThisSkill->count()); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Всего привязок:</span>
                            <span class="text-[#c1a96e] font-semibold"><?php echo e($mobSkill->mobSkills->count()); ?></span>
                        </div>
                    </div>
                </div>

                
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h3 class="text-lg font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-xl mr-3">⚙️</span>
                            Действия
                        </h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="<?php echo e(route('admin.mob-skills.edit', $mobSkill)); ?>" 
                           class="w-full bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-4 py-3 rounded-lg border border-[#3b3629] transition duration-300 flex items-center justify-center">
                            ✏️ Редактировать скилл
                        </a>
                        <a href="<?php echo e(route('admin.mob-skills.assign', $mobSkill)); ?>" 
                           class="w-full bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] hover:from-[#4a452c] hover:to-[#3d3a2e] text-[#f8eac2] px-4 py-3 rounded-lg border border-[#3b3629] transition duration-300 flex items-center justify-center">
                            🔗 Привязать к мобам
                        </a>
                        <form method="POST" action="<?php echo e(route('admin.mob-skills.destroy', $mobSkill)); ?>" 
                              onsubmit="return confirm('Вы уверены, что хотите удалить этот скилл? Это действие нельзя отменить.')" 
                              class="w-full">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" 
                                    class="w-full bg-gradient-to-b from-[#59372d] to-[#3c221b] hover:from-[#6e3f35] hover:to-[#4a2a20] text-[#f8eac2] px-4 py-3 rounded-lg border border-[#6e3f35] transition duration-300 flex items-center justify-center">
                                🗑️ Удалить скилл
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        
        <?php if($mobsWithThisSkill->count() > 0): ?>
        <div class="mt-8">
            <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                <div class="px-6 py-4 border-b border-[#3b3629]">
                    <h3 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                        <span class="text-2xl mr-3">👹</span>
                        Мобы, использующие этот скилл (<?php echo e($mobsWithThisSkill->count()); ?>)
                    </h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-[#1a1814] border-b border-[#3b3629]">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">Моб</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">Локация</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">HP</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">Уровень</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">Действия</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-[#3b3629]">
                            <?php $__currentLoopData = $mobsWithThisSkill; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mob): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="hover:bg-[#2a2722] transition duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <?php if($mob->icon): ?>
                                            <img src="<?php echo e(asset($mob->icon)); ?>" alt="<?php echo e($mob->name); ?>" class="w-8 h-8 mr-3 rounded">
                                        <?php endif; ?>
                                        <div>
                                            <div class="text-sm font-medium text-[#d4cbb0]"><?php echo e($mob->name); ?></div>
                                            <div class="text-sm text-[#998d66]">ID: <?php echo e($mob->id); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                    <?php echo e($mob->location ?? 'Не указана'); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                    <?php echo e($mob->hp); ?>/<?php echo e($mob->max_hp); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                    <?php echo e($mob->strength + $mob->defense + $mob->agility + $mob->vitality + $mob->intelligence); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="<?php echo e(route('admin.mobs.show', $mob)); ?>" 
                                       class="text-[#c1a96e] hover:text-[#e4d7b0] transition duration-200">
                                        Просмотр
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/mob-skills/show.blade.php ENDPATH**/ ?>