<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Привязка скиллов к мобам - Админ панель</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="bg-[#1a1814] text-[#d4cbb0] min-h-screen">
    <div class="container mx-auto px-4 py-8">
        
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-[#e4d7b0] mb-2">🔗 Привязка скиллов к мобам</h1>
                    <p class="text-[#998d66]">Назначение скиллов конкретным мобам в игре</p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('admin.mob-skills.index')); ?>" 
                       class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] hover:from-[#3b3629] hover:to-[#2a2722] text-[#d4cbb0] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ← Назад к списку скиллов
                    </a>
                </div>
            </div>
        </div>

        
        <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-[#3b3629]">
                <h2 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                    <span class="text-2xl mr-3">👹</span>
                    Выбор моба
                </h2>
            </div>
            <div class="p-6">
                <form method="GET" action="<?php echo e(route('admin.mob-skills.assign')); ?>" class="flex items-end space-x-4">
                    <div class="flex-1">
                        <label for="mob_id" class="block text-sm font-medium text-[#c1a96e] mb-2">Выберите моба</label>
                        <select id="mob_id" name="mob_id" 
                                class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            <option value="">-- Выберите моба --</option>
                            <?php $__currentLoopData = $mobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mobOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($mobOption->id); ?>" <?php echo e(request('mob_id') == $mobOption->id ? 'selected' : ''); ?>>
                                    <?php echo e($mobOption->name); ?> (ID: <?php echo e($mobOption->id); ?>) - <?php echo e($mobOption->location ?? 'Без локации'); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <button type="submit" 
                            class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300">
                        🔍 Выбрать
                    </button>
                </form>
            </div>
        </div>

        <?php if($mob): ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div class="lg:col-span-2">
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h3 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-2xl mr-3">📋</span>
                            Информация о мобе: <?php echo e($mob->name); ?>

                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="text-center">
                                <div class="text-sm text-[#c1a96e] mb-1">HP</div>
                                <div class="text-lg font-semibold text-[#d4cbb0]"><?php echo e($mob->hp); ?>/<?php echo e($mob->max_hp); ?></div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-[#c1a96e] mb-1">Сила</div>
                                <div class="text-lg font-semibold text-[#d4cbb0]"><?php echo e($mob->strength); ?></div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-[#c1a96e] mb-1">Защита</div>
                                <div class="text-lg font-semibold text-[#d4cbb0]"><?php echo e($mob->defense); ?></div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-[#c1a96e] mb-1">Локация</div>
                                <div class="text-sm text-[#d4cbb0]"><?php echo e($mob->location ?? 'Не указана'); ?></div>
                            </div>
                        </div>

                        
                        <?php if($mob->skills->count() > 0): ?>
                        <div class="border-t border-[#3b3629] pt-6">
                            <h4 class="text-lg font-semibold text-[#e4d7b0] mb-4">Текущие скиллы (<?php echo e($mob->skills->count()); ?>)</h4>
                            <div class="space-y-3">
                                <?php $__currentLoopData = $mob->skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mobSkill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg p-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <?php if($mobSkill->skillTemplate): ?>
                                            <div class="flex items-center">
                                                <?php if($mobSkill->skillTemplate->icon): ?>
                                                    <img src="<?php echo e(asset($mobSkill->skillTemplate->icon)); ?>" alt="<?php echo e($mobSkill->skillTemplate->name); ?>" class="w-8 h-8 mr-3">
                                                <?php endif; ?>
                                                <div>
                                                    <div class="font-semibold text-[#d4cbb0]"><?php echo e($mobSkill->skillTemplate->name); ?></div>
                                                    <div class="text-sm text-[#998d66]">
                                                        Шанс: <?php echo e($mobSkill->chance ?? $mobSkill->skillTemplate->chance); ?>% | 
                                                        Тип: <?php echo e(ucfirst($mobSkill->skillTemplate->effect_type)); ?>

                                                    </div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-[#998d66]">Шаблон скилла не найден</div>
                                        <?php endif; ?>
                                    </div>
                                    <form method="POST" action="<?php echo e(route('admin.mob-skills.detach')); ?>" 
                                          onsubmit="return confirm('Отвязать этот скилл от моба?')" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <input type="hidden" name="mob_skill_id" value="<?php echo e($mobSkill->id); ?>">
                                        <button type="submit" 
                                                class="bg-gradient-to-b from-[#59372d] to-[#3c221b] hover:from-[#6e3f35] hover:to-[#4a2a20] text-[#f8eac2] px-3 py-2 rounded border border-[#6e3f35] transition duration-300 text-sm">
                                            🗑️ Отвязать
                                        </button>
                                    </form>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="border-t border-[#3b3629] pt-6 text-center">
                            <div class="text-4xl mb-2">⚔️</div>
                            <div class="text-[#998d66]">У этого моба пока нет скиллов</div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            
            <div>
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h3 class="text-lg font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-xl mr-3">📊</span>
                            Статистика
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Скиллов у моба:</span>
                            <span class="text-[#c1a96e] font-semibold"><?php echo e($mob->skills->count()); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Доступно шаблонов:</span>
                            <span class="text-[#c1a96e] font-semibold"><?php echo e($templates->count()); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Общий уровень:</span>
                            <span class="text-[#c1a96e] font-semibold"><?php echo e($mob->strength + $mob->defense + $mob->agility + $mob->vitality + $mob->intelligence); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
            <div class="px-6 py-4 border-b border-[#3b3629]">
                <h3 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                    <span class="text-2xl mr-3">⚔️</span>
                    Доступные скиллы для привязки
                </h3>
            </div>
            <div class="p-6">
                <?php if($templates->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $alreadyAssigned = $mob->skills->where('skill_template_id', $template->id)->first();
                    ?>
                    <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg p-4 <?php echo e($alreadyAssigned ? 'opacity-50' : ''); ?>">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex items-center">
                                <?php if($template->icon): ?>
                                    <img src="<?php echo e(asset($template->icon)); ?>" alt="<?php echo e($template->name); ?>" class="w-10 h-10 mr-3">
                                <?php endif; ?>
                                <div>
                                    <h4 class="font-semibold text-[#d4cbb0]"><?php echo e($template->name); ?></h4>
                                    <div class="text-sm text-[#998d66]"><?php echo e(ucfirst($template->effect_type)); ?></div>
                                </div>
                            </div>
                            <span class="inline-block px-2 py-1 rounded text-xs font-medium
                                <?php if($template->effect_type === 'stun'): ?> bg-red-900/30 text-red-300 border border-red-700
                                <?php elseif($template->effect_type === 'buff'): ?> bg-green-900/30 text-green-300 border border-green-700
                                <?php elseif($template->effect_type === 'debuff'): ?> bg-orange-900/30 text-orange-300 border border-orange-700
                                <?php elseif($template->effect_type === 'dot'): ?> bg-purple-900/30 text-purple-300 border border-purple-700
                                <?php else: ?> bg-gray-900/30 text-gray-300 border border-gray-700
                                <?php endif; ?>">
                                <?php echo e($template->priority); ?>

                            </span>
                        </div>

                        <p class="text-sm text-[#998d66] mb-4 line-clamp-2"><?php echo e($template->description); ?></p>

                        <div class="grid grid-cols-3 gap-2 text-xs text-center mb-4">
                            <div>
                                <div class="text-[#c1a96e]">Шанс</div>
                                <div class="text-[#d4cbb0]"><?php echo e($template->chance); ?>%</div>
                            </div>
                            <div>
                                <div class="text-[#c1a96e]">Кулдаун</div>
                                <div class="text-[#d4cbb0]"><?php echo e($template->cooldown); ?>с</div>
                            </div>
                            <div>
                                <div class="text-[#c1a96e]">Длительность</div>
                                <div class="text-[#d4cbb0]"><?php echo e($template->duration); ?>с</div>
                            </div>
                        </div>

                        <?php if($alreadyAssigned): ?>
                            <div class="text-center text-sm text-[#998d66] py-2">
                                ✓ Уже привязан к мобу
                            </div>
                        <?php else: ?>
                            <form method="POST" action="<?php echo e(route('admin.mob-skills.attach')); ?>">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="mob_id" value="<?php echo e($mob->id); ?>">
                                <input type="hidden" name="skill_template_id" value="<?php echo e($template->id); ?>">
                                
                                <div class="mb-3">
                                    <label class="block text-xs text-[#c1a96e] mb-1">Шанс срабатывания (%)</label>
                                    <input type="number" name="chance" min="1" max="100" value="<?php echo e($template->chance); ?>"
                                           class="w-full bg-[#2a2722] border border-[#3b3629] rounded px-2 py-1 text-sm text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                                </div>
                                
                                <button type="submit" 
                                        class="w-full bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-3 py-2 rounded border border-[#3b3629] transition duration-300 text-sm">
                                    🔗 Привязать к мобу
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">⚔️</div>
                    <h3 class="text-xl font-semibold text-[#e4d7b0] mb-2">Нет доступных скиллов</h3>
                    <p class="text-[#998d66] mb-6">Сначала создайте шаблоны скиллов</p>
                    <a href="<?php echo e(route('admin.mob-skills.create')); ?>" 
                       class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300">
                        ✨ Создать скилл
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php else: ?>
        
        <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
            <div class="p-12 text-center">
                <div class="text-6xl mb-4">👹</div>
                <h3 class="text-xl font-semibold text-[#e4d7b0] mb-2">Выберите моба</h3>
                <p class="text-[#998d66]">Для привязки скиллов сначала выберите моба из списка выше</p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/mob-skills/assign.blade.php ENDPATH**/ ?>