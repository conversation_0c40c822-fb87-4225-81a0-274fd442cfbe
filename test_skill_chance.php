<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ТЕСТ ШАНСА СРАБАТЫВАНИЯ СКИЛЛОВ ===\n\n";

// Получаем тестового игрока
$user = \App\Models\User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

// Создаем тестовый шаблон скилла с низким шансом
$testTemplate = \App\Models\MobSkillTemplate::create([
    'name' => 'Тестовый скилл (низкий шанс)',
    'description' => 'Скилл для тестирования шанса срабатывания',
    'effect_type' => 'damage',
    'chance' => 20, // 20% шанс
    'cooldown' => 5, // 5 секунд кулдаун
    'duration' => 0,
    'target_type' => 'player',
    'min_health_percent' => 0,
    'max_health_percent' => 100,
    'is_active' => true,
    'priority' => 5,
    'effect_data' => json_encode(['damage' => 15])
]);

echo "✅ Создан тестовый шаблон скилла: {$testTemplate->name} (ID: {$testTemplate->id})\n";
echo "   Шанс: {$testTemplate->chance}%\n";
echo "   Кулдаун: {$testTemplate->cooldown}с\n\n";

// Получаем тестового моба
$mob = \App\Models\Mob::whereNotNull('mine_location_id')->first();
if (!$mob) {
    echo "❌ Не найден моб в руднике!\n";
    exit(1);
}

// Привязываем скилл к мобу с разными шансами
$mobSkill1 = \App\Models\MobSkill::create([
    'mob_id' => $mob->id,
    'skill_template_id' => $testTemplate->id,
    'chance' => null // Будет использоваться шанс из шаблона (20%)
]);

$mobSkill2 = \App\Models\MobSkill::create([
    'mob_id' => $mob->id,
    'skill_template_id' => $testTemplate->id,
    'chance' => 50 // Индивидуальный шанс 50%
]);

echo "✅ Привязаны скиллы к мобу {$mob->name}:\n";
echo "   1. Скилл с шансом шаблона (20%)\n";
echo "   2. Скилл с индивидуальным шансом (50%)\n\n";

// Тестируем MobSkillFramework
$mobSkillFramework = app(\App\Services\MobSkillFramework::class);

$context = [
    'event_type' => 'attack',
    'mine_location_id' => $mob->mine_location_id,
    'target_type' => 'player',
    'is_mine_mob' => true
];

echo "🎲 ТЕСТИРУЕМ ШАНСЫ СРАБАТЫВАНИЯ (20 попыток):\n\n";

$stats = [
    'total_attempts' => 20,
    'skill1_activations' => 0,
    'skill2_activations' => 0,
    'total_activations' => 0
];

for ($i = 1; $i <= 20; $i++) {
    echo "Попытка #{$i}: ";
    
    // Обновляем моба с новыми скиллами
    $mob->refresh();
    $mob->load('skills.skillTemplate');
    
    $activatedSkills = $mobSkillFramework->processMobSkills($mob, $user, $context);
    
    if (empty($activatedSkills)) {
        echo "❌ Не активировано\n";
    } else {
        echo "✅ Активировано: " . count($activatedSkills) . " скилл(ов)\n";
        $stats['total_activations']++;
        
        foreach ($activatedSkills as $skill) {
            // Определяем какой скилл активировался по данным
            if (isset($skill['message'])) {
                echo "   - {$skill['message']}\n";
            }
        }
    }
    
    // Небольшая пауза между попытками
    usleep(100000); // 0.1 секунды
}

echo "\n📊 СТАТИСТИКА:\n";
echo "   Всего попыток: {$stats['total_attempts']}\n";
echo "   Активаций: {$stats['total_activations']}\n";
echo "   Процент активации: " . round(($stats['total_activations'] / $stats['total_attempts']) * 100, 1) . "%\n";
echo "   Ожидаемый процент: ~35% (среднее между 20% и 50%)\n\n";

// Очищаем тестовые данные
echo "🧹 ОЧИСТКА ТЕСТОВЫХ ДАННЫХ:\n";
$mobSkill1->delete();
$mobSkill2->delete();
$testTemplate->delete();
echo "   ✅ Тестовые данные удалены\n\n";

echo "=== ТЕСТ ЗАВЕРШЕН ===\n";
