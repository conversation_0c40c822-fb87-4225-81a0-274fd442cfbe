<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Административная панель - Echoes of Eternity</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-[#211f1a] text-[#d9d3b8] font-serif">
    
    <div class="min-h-screen flex flex-col">
        
        <main class="flex-grow container mx-auto px-4 py-6">
            
            <div
                class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg mb-8 p-5">
                
                <div class="flex justify-between items-center mb-5">
                    
                    <a href="/home" title="На главную сайта"
                        class="p-2 bg-[#514b3c] hover:bg-[#a6925e] rounded-lg transition duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#e5b769]" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                    </a>

                    
                    <h1 class="text-3xl font-bold text-[#e5b769]">Панель Управления</h1>

                    
                    <button onclick="location.reload()" title="Обновить страницу"
                        class="p-2 bg-[#514b3c] hover:bg-[#a6925e] rounded-lg transition duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#e5b769]" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </button>
                </div>

                
                <div class="flex justify-between items-center mb-5 bg-[#2a2721] p-4 rounded-lg border border-[#514b3c]">
                    <div class="flex items-center">
                        <div
                            class="w-12 h-12 flex items-center justify-center bg-[#4a452c] rounded-full border-2 border-[#a6925e] mr-4">
                            <span class="text-2xl">👑</span>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-[#e5b769]">
                                <?php echo e(auth()->user()->name ?? 'Администратор'); ?>

                            </h2>
                            <span class="text-sm text-[#9a9483]">Роль: Администратор</span>
                        </div>
                    </div>

                    
                    <div class="flex items-center space-x-3">
                        <div class="flex items-center" title="Золото">
                            <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="G" class="w-5 h-5 mr-1">
                            <span
                                class="text-sm font-medium text-[#e5b769]"><?php echo e(number_format($userProfile->gold ?? 0, 0, ',', ' ')); ?></span>
                        </div>
                        <div class="flex items-center" title="Серебро">
                            <img src="<?php echo e(asset('assets/silverIcon.png')); ?>" alt="S" class="w-5 h-5 mr-1">
                            <span
                                class="text-sm font-medium text-[#c0c0c0]"><?php echo e(number_format($userProfile->silver ?? 0, 0, ',', ' ')); ?></span>
                        </div>
                        <div class="flex items-center" title="Бронза">
                            <img src="<?php echo e(asset('assets/bronzeIcon.png')); ?>" alt="B" class="w-5 h-5 mr-1">
                            <span
                                class="text-sm font-medium text-[#cd7f32]"><?php echo e(number_format($userProfile->bronze ?? 0, 0, ',', ' ')); ?></span>
                        </div>
                    </div>
                </div>

                
                <?php if(session('welcome_message')): ?>
                    <div class="bg-[#38352c] text-[#e5b769] p-3 rounded-lg border border-[#514b3c] mb-5 text-center">
                        <?php echo e(session('welcome_message')); ?>

                    </div>
                <?php endif; ?>

                
                <div class="grid grid-cols-3 gap-4 text-center bg-[#2a2721] p-3 rounded-lg border border-[#514b3c]">
                    <div>
                        <span class="text-xs text-[#9a9483] uppercase block">Онлайн</span>
                        <a href="<?php echo e(route('online.users')); ?>"
                            class="text-lg font-semibold text-[#7cfc00] hover:text-[#90ee90]"><?php echo e($onlineCount ?? 0); ?></a>
                    </div>
                    <div>
                        <span class="text-xs text-[#9a9483] uppercase block">Время сервера</span>
                        <span class="text-lg font-semibold text-[#e5b769]" id="server-time"><?php echo e(date('H:i:s')); ?></span>
                    </div>
                    <div>
                        <span class="text-xs text-[#9a9483] uppercase block">Дата</span>
                        <span class="text-lg font-semibold text-[#e5b769]"><?php echo e(date('d.m.Y')); ?></span>
                    </div>
                </div>
            </div>

            
            <div class="container max-w-7xl mx-auto mt-8">
                
                <h2
                    class="text-center text-2xl font-bold text-[#e5b769] py-3 mb-6 bg-[#38352c] rounded-lg border border-[#514b3c]">
                    Разделы Управления
                </h2>

                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                ⚔️</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Предметы</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление игровыми предметами: оружие, броня,
                            ресурсы и т.д.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.items.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Все
                                предметы</a>
                            <a href="<?php echo e(route('admin.items.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Создать</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🛒</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Магазин (Товары)</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление товарами, выставленными на продажу в
                            игровом магазине.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.shop.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Все
                                товары</a>
                            <a href="<?php echo e(route('admin.shop.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Добавить</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                👹</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Мобы</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Настройка характеристик мобов, их дропа
                            (ресурсы, предметы, валюта, алхимия).</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.mobs.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Все
                                мобы</a>
                            <a href="<?php echo e(route('admin.mobs.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Создать</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                ⚔️</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Скиллы мобов</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Создание и настройка скиллов для мобов: стан,
                            урон, баффы и другие эффекты.</p>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.mob-skills.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                📋 Все скиллы
                            </a>
                            <a href="<?php echo e(route('admin.mob-skills.assign')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                🔗 Привязать к мобу
                            </a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.mob-skills.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Создать скилл
                            </a>
                            <a href="<?php echo e(route('admin.mob-skills.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Управление
                            </a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🤖</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Боты</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление NPC-ботами: создание, настройка
                            характеристик, локаций, активности.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.bots.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Все
                                боты</a>
                            <a href="<?php echo e(route('admin.bots.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Создать</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🏘️</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Функционал локаций</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление функциональными элементами локаций:
                            деревни, обелиски и другие объекты.</p>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.locations.villages.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                🏘️ Деревни
                            </a>
                            <a href="<?php echo e(route('admin.locations.obelisks.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                🔮 Обелиски
                            </a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.locations.villages.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Создать деревню
                            </a>
                            <a href="<?php echo e(route('admin.locations.obelisks.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Создать обелиск
                            </a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🏞️</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Локации</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление игровыми локациями: создание новых,
                            редактирование существующих, настройка требований.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.locations.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Все
                                локации</a>
                            <a href="<?php echo e(route('admin.locations.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Создать</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                            </div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Рудники</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление локациями рудников: создание новых
                            шахт,
                            редактирование, настройка ресурсов и добавление подлокаций.</p>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.mine-locations.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Локации рудников
                            </a>
                            <a href="<?php echo e(route('admin.location-resources.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Ресурсы локаций
                            </a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.mine-locations.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Создать локацию
                            </a>
                            <a href="<?php echo e(route('admin.location-resources.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Добавить ресурс
                            </a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🏰</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Аванпосты</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление локациями аванпостов: создание новых
                            аванпостов,
                            редактирование, настройка обелисков и добавление подлокаций.</p>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.outpost-locations.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                🏰 Локации аванпостов
                            </a>
                            <a href="<?php echo e(route('admin.locations.obelisks.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                🔮 Обелиски
                            </a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.outpost-locations.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Создать аванпост
                            </a>
                            <a href="<?php echo e(route('admin.locations.obelisks.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Создать обелиск
                            </a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🏛️</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Подземелья</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление подземельями: создание новых
                            подземелий,
                            редактирование описаний, настройка требований и управление наградами.</p>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.dungeons.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                🏛️ Список подземелий
                            </a>
                            <a href="<?php echo e(route('admin.dungeons.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                💎 Управление наградами
                            </a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.dungeons.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Создать подземелье
                            </a>
                            <a href="<?php echo e(route('admin.dungeons.statistics')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Статистика
                            </a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                ⚗️</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Алхимия и Зелья</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление всеми аспектами алхимии:
                            ингредиенты, катализаторы, рецепты и зелья. Создание шаблонов зелий для выдачи игрокам.</p>
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <a href="<?php echo e(route('admin.alchemy-ingredients.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">🌿
                                Ингредиенты</a>
                            <a href="<?php echo e(route('admin.alchemy-catalysts.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">✨
                                Катализаторы</a>
                            <a href="<?php echo e(route('admin.potion-recipes.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">📜
                                Рецепты</a>
                            <a href="<?php echo e(route('admin.potions.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">🧪
                                Шаблоны зелий</a>
                            <a href="<?php echo e(route('admin.alchemy-ingredients.give')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">🎁
                                Выдать ингредиенты</a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.alchemy.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Обзор</a>
                            <a href="<?php echo e(route('admin.alchemy.analytics')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Аналитика</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                👤</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Игроки</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Просмотр списка игроков, их профилей и
                            статистики.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.users.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Все
                                игроки</a>
                            <a href="<?php echo e(route('online.users')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Онлайн</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🎒</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Предметы игроков</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление предметами игроков: просмотр,
                            редактирование, поиск по ID.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.player-items.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Список
                                игроков</a>
                            <a href="<?php echo e(route('admin.player-items.search')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Поиск
                                предметов</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🔨</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Кузнец</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление кузнецом: настройка ресурсов для
                            улучшения и ремонта, управление слотами игроков.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.blacksmith.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Настройки</a>
                            <a href="<?php echo e(route('admin.blacksmith.slots')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Слоты
                                игроков</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                💎</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Ресурсы</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление игровыми ресурсами: руды, кристаллы,
                            травы и другие материалы для крафта и улучшения.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.resources.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Все
                                ресурсы</a>
                            <a href="<?php echo e(route('admin.resources.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Создать</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                            </div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Ресурсы локаций</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление ресурсами в локациях: добавление,
                            редактирование,
                            респаун и генерация новых ресурсов.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.location-resources.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Все
                                ресурсы</a>
                            <a href="<?php echo e(route('admin.location-resources.create')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Добавить</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🌱</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Семена</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление семенами для фермерства: создание
                            шаблонов семян,
                            настройка характеристик и выдача игрокам.</p>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.farming-seeds.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                📋 Все семена
                            </a>
                            <a href="<?php echo e(route('admin.farming-seeds.create')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                ➕ Создать
                            </a>
                        </div>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.farming-seeds.give')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                🎁 Выдать
                            </a>
                            <a href="<?php echo e(route('admin.farming-seeds.player-seeds')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                👤 Семена игроков
                            </a>
                        </div>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.harvest-chances.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                🌱 Шансы урожая
                            </a>
                            <a href="<?php echo e(route('admin.seed-upgrade-settings.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                ⚡ Настройки слияния
                            </a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🌾</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Урожай</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление урожаем для фермерства: создание
                            шаблонов урожая, настройка характеристик и выдача игрокам.</p>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.harvests.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                📋 Весь урожай
                            </a>
                            <a href="<?php echo e(route('admin.harvests.create')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                ➕ Создать
                            </a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.harvest-chances.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Шансы выпадения урожая
                            </a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                🔑</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Ключи доступа</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Управление системой временного доступа:
                            создание, просмотр и управление ключами доступа.</p>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <a href="<?php echo e(route('admin.access-keys.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                📋 Все ключи
                            </a>
                            <a href="<?php echo e(route('admin.access-keys.create')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                ➕ Создать ключ
                            </a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.dashboard')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Статистика</a>
                            <a href="<?php echo e(route('admin.access-keys.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Управление</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                ⚙️</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Настройки</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Общие настройки игры, системные параметры.</p>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="#"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Основные</a>
                            <a href="#"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">Дополнительно</a>
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-br from-[#3d3a2e] to-[#2a2721] rounded-lg p-5 border border-[#514b3c] hover:border-[#a6925e] hover:shadow-xl transition duration-300 flex flex-col">
                        <div class="flex items-center mb-3">
                            <div
                                class="w-10 h-10 rounded-full bg-[#4a452c] border border-[#a6925e] flex items-center justify-center mr-3 text-xl">
                                📋</div>
                            <h3 class="text-xl font-bold text-[#e5b769]">Дополнительное</h3>
                        </div>
                        <p class="text-sm text-[#b0a890] mb-4 flex-grow">Дополнительные инструменты и функции
                            администрирования.</p>
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <a href="<?php echo e(route('admin.deleted-messages.index')); ?>"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                📩 Удаленные сообщения
                            </a>
                            <a href="#"
                                class="text-center bg-[#514b3c] text-xs py-2 px-2 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                📊 Статистика
                            </a>
                        </div>
                        <div class="flex justify-between space-x-2 mt-auto">
                            <a href="<?php echo e(route('admin.deleted-messages.index')); ?>"
                                class="flex-1 text-center bg-[#514b3c] text-sm py-2 px-3 rounded border border-[#8c784e] hover:bg-[#a6925e] hover:text-[#2f2d2b] transition duration-300">
                                Удаленные сообщения
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        
        <footer class="bg-[#2a2721] text-[#9a9483] py-4 mt-auto border-t border-[#514b3c]">
            <div class="container max-w-7xl mx-auto px-6 text-center text-sm">
                © <?php echo e(date('Y')); ?> Echoes of Eternity | Административная Панель
            </div>
        </footer>
    </div>

    
    <script>
        /**
         * Функция для обновления отображения времени сервера на странице.
         */
        function updateServerTime() {
            // Находим элемент для отображения времени
            const timeElement = document.getElementById('server-time');
            // Проверяем, найден ли элемент
            if (timeElement) {
                // Получаем текущее время
                const now = new Date();
                // Форматируем часы, минуты, секунды (добавляем ведущий 0, если нужно)
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                // Обновляем текстовое содержимое элемента
                timeElement.textContent = `${hours}:${minutes}:${seconds}`;
            }
        }
        // Устанавливаем интервал для вызова функции обновления каждую секунду (1000 мс)
        setInterval(updateServerTime, 1000);
        // Вызываем функцию сразу при загрузке страницы, чтобы время отобразилось немедленно
        updateServerTime();
    </script>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>