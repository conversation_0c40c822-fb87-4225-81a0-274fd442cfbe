<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Создать скилл моба - Админ панель</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="bg-[#1a1814] text-[#d4cbb0] min-h-screen">
    <div class="container mx-auto px-4 py-8">
        
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-[#e4d7b0] mb-2">✨ Создать новый скилл моба</h1>
                    <p class="text-[#998d66]">Настройте параметры нового скилла для мобов</p>
                </div>
                <a href="<?php echo e(route('admin.mob-skills.index')); ?>" 
                   class="bg-[#2a2722] hover:bg-[#3b3629] text-[#f8eac2] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                    ← Назад к списку
                </a>
            </div>
        </div>

        <form action="<?php echo e(route('admin.mob-skills.store')); ?>" method="POST" class="space-y-8">
            <?php echo csrf_field(); ?>

            
            <div class="bg-[#2a2721] p-6 rounded-lg border border-[#3b3629]">
                <h3 class="text-xl font-bold text-[#e4d7b0] mb-6">📋 Основная информация</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-[#e4d7b0] mb-2">Название скилла *</label>
                        <input type="text" id="name" name="name" value="<?php echo e(old('name')); ?>" required
                               class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none"
                               placeholder="Например: Тяжелый удар">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="icon" class="block text-sm font-medium text-[#e4d7b0] mb-2">Иконка</label>
                        <select id="icon" name="icon" 
                                class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                            <option value="">Выберите иконку</option>
                            <?php $__currentLoopData = $icons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $icon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($icon); ?>" <?php echo e(old('icon') == $icon ? 'selected' : ''); ?>>
                                    <?php echo e(basename($icon)); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-[#e4d7b0] mb-2">Описание</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none"
                              placeholder="Описание эффекта скилла..."><?php echo e(old('description')); ?></textarea>
                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            
            <div class="bg-[#2a2721] p-6 rounded-lg border border-[#3b3629]">
                <h3 class="text-xl font-bold text-[#e4d7b0] mb-6">⚡ Параметры эффекта</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="effect_type" class="block text-sm font-medium text-[#e4d7b0] mb-2">Тип эффекта *</label>
                        <select id="effect_type" name="effect_type" required
                                class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                            <option value="">Выберите тип эффекта</option>
                            <?php $__currentLoopData = $effectTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(old('effect_type') == $key ? 'selected' : ''); ?>>
                                    <?php echo e($name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['effect_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="target_type" class="block text-sm font-medium text-[#e4d7b0] mb-2">Тип цели *</label>
                        <select id="target_type" name="target_type" required
                                class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                            <?php $__currentLoopData = $targetTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(old('target_type', 'player') == $key ? 'selected' : ''); ?>>
                                    <?php echo e($name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['target_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="mt-6">
                    <label for="effect_data" class="block text-sm font-medium text-[#e4d7b0] mb-2">Данные эффекта (JSON)</label>
                    <textarea id="effect_data" name="effect_data" rows="4"
                              class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none font-mono text-sm"
                              placeholder='{"duration": 5, "message": "Эффект применен!"}'><?php echo old('effect_data'); ?></textarea>
                    <p class="text-[#998d66] text-sm mt-1">Введите JSON с параметрами эффекта. Например: {"duration": 5, "damage": 50}</p>
                    <?php $__errorArgs = ['effect_data'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            
            <div class="bg-[#2a2721] p-6 rounded-lg border border-[#3b3629]">
                <h3 class="text-xl font-bold text-[#e4d7b0] mb-6">🎯 Параметры использования</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="chance" class="block text-sm font-medium text-[#e4d7b0] mb-2">Шанс использования (%) *</label>
                        <input type="number" id="chance" name="chance" value="<?php echo e(old('chance', 15)); ?>" required
                               min="1" max="100"
                               class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                        <?php $__errorArgs = ['chance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="cooldown" class="block text-sm font-medium text-[#e4d7b0] mb-2">Кулдаун (сек) *</label>
                        <input type="number" id="cooldown" name="cooldown" value="<?php echo e(old('cooldown', 30)); ?>" required
                               min="0"
                               class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                        <?php $__errorArgs = ['cooldown'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="duration" class="block text-sm font-medium text-[#e4d7b0] mb-2">Длительность (сек) *</label>
                        <input type="number" id="duration" name="duration" value="<?php echo e(old('duration', 5)); ?>" required
                               min="0"
                               class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                        <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                    <div>
                        <label for="min_health_percent" class="block text-sm font-medium text-[#e4d7b0] mb-2">Мин. здоровье (%) *</label>
                        <input type="number" id="min_health_percent" name="min_health_percent" value="<?php echo e(old('min_health_percent', 0)); ?>" required
                               min="0" max="100"
                               class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                        <?php $__errorArgs = ['min_health_percent'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="max_health_percent" class="block text-sm font-medium text-[#e4d7b0] mb-2">Макс. здоровье (%) *</label>
                        <input type="number" id="max_health_percent" name="max_health_percent" value="<?php echo e(old('max_health_percent', 100)); ?>" required
                               min="0" max="100"
                               class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                        <?php $__errorArgs = ['max_health_percent'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="priority" class="block text-sm font-medium text-[#e4d7b0] mb-2">Приоритет (1-10) *</label>
                        <input type="number" id="priority" name="priority" value="<?php echo e(old('priority', 5)); ?>" required
                               min="1" max="10"
                               class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                        <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="mt-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" <?php echo e(old('is_active', true) ? 'checked' : ''); ?>

                               class="rounded border-[#3b3629] text-[#c1a96e] focus:border-[#c1a96e] focus:ring-[#c1a96e]">
                        <span class="ml-2 text-[#e4d7b0]">Активировать скилл сразу после создания</span>
                    </label>
                </div>
            </div>

            
            <div class="flex justify-end space-x-4">
                <a href="<?php echo e(route('admin.mob-skills.index')); ?>" 
                   class="bg-[#2a2722] hover:bg-[#3b3629] text-[#f8eac2] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300">
                    Отмена
                </a>
                <button type="submit" 
                        class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-8 py-3 rounded-lg border border-[#3b3629] transition duration-300 shadow-lg">
                    ✨ Создать скилл
                </button>
            </div>
        </form>
    </div>

    
    <?php if($errors->any()): ?>
        <div class="fixed top-4 right-4 bg-red-900 border border-red-700 text-red-300 px-6 py-4 rounded-lg shadow-lg z-50">
            <div class="flex items-center">
                <span class="text-xl mr-3">❌</span>
                <div>
                    <p class="font-semibold">Ошибки в форме:</p>
                    <ul class="list-disc list-inside text-sm mt-1">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script>
        // Автоматически скрывать уведомления через 10 секунд
        setTimeout(() => {
            const notifications = document.querySelectorAll('.fixed.top-4.right-4');
            notifications.forEach(notification => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            });
        }, 10000);

        // Предзаполнение JSON для разных типов эффектов
        document.getElementById('effect_type').addEventListener('change', function() {
            const effectData = document.getElementById('effect_data');
            const templates = {
                'stun': '{\n  "duration": 5,\n  "disable_skills": true,\n  "disable_movement": true,\n  "message": "⚡ Вы оглушены!"\n}',
                'damage': '{\n  "damage": 50,\n  "message": "⚔️ Дополнительный урон!"\n}',
                'heal': '{\n  "heal_amount": 50,\n  "heal_percent": 15,\n  "message": "✨ Восстановление здоровья!"\n}',
                'buff': '{\n  "damage_multiplier": 1.5,\n  "duration": 10,\n  "message": "🛡️ Положительный эффект!"\n}',
                'debuff': '{\n  "damage_reduction": 0.3,\n  "duration": 15,\n  "message": "💀 Отрицательный эффект!"\n}',
                'dot': '{\n  "damage_per_tick": 10,\n  "tick_interval": 2,\n  "total_duration": 12,\n  "message": "🔥 Урон со временем!"\n}',
                'hot': '{\n  "heal_per_tick": 15,\n  "tick_interval": 3,\n  "total_duration": 15,\n  "message": "✨ Лечение со временем!"\n}'
            };
            
            if (templates[this.value] && !effectData.value.trim()) {
                effectData.value = templates[this.value];
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/mob-skills/create.blade.php ENDPATH**/ ?>