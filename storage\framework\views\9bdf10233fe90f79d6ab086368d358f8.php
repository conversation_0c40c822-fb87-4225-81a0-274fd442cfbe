<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Список мобов - Админка</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    
    <div class="min-h-screen flex flex-col">
        
        <main class="flex-grow p-2 md:p-4">
            <div class="w-full bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg">
                
                <div class="flex items-center justify-between px-4 py-2">
                    
                    <a href="<?php echo e(route('admin.dashboard')); ?>"
                        class="w-8 h-8 bg-[#a6925e] flex items-center justify-center rounded hover:bg-[#e5b769]">
                        🏠
                    </a>
                    <div class="flex items-center space-x-4">
                        
                        <div class="flex items-center space-x-1">
                            <img src="<?php echo e(asset('assets/gold.png')); ?>" alt="Золото" class="w-6 h-6">
                            <span class="text-sm text-[#e5b769] font-medium"><?php echo e($userProfile->gold); ?></span>
                        </div>

                        
                        <div class="flex items-center space-x-1">
                            <img src="<?php echo e(asset('assets/gold.png')); ?>" alt="Серебро" class="w-6 h-6">
                            <span class="text-sm text-[#d9d3b8] font-medium"><?php echo e($userProfile->silver); ?></span>
                        </div>

                        
                        <div class="flex items-center space-x-1">
                            <img src="<?php echo e(asset('assets/gold.png')); ?>" alt="Бронза" class="w-6 h-6">
                            <span class="text-sm text-[#c89d58] font-medium"><?php echo e($userProfile->bronze); ?></span>
                        </div>
                    </div>
                    
                    <button onclick="location.reload()"
                        class="w-8 h-8 bg-[#a6925e] flex items-center justify-center rounded-full hover:bg-[#e5b769]">
                        𖦹
                    </button>
                </div>

                
                <?php if(session('success')): ?>
                    <div class="bg-[#5e7ba6] text-white p-2 m-2 rounded">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                
                <div class="px-4 py-2 flex justify-between items-center border-b border-[#a6925e]">
                    <h1 class="text-2xl font-bold text-[#e5b769]">Список мобов</h1>
                    <a href="<?php echo e(route('admin.mobs.create')); ?>"
                        class="bg-[#5e7ba6] hover:bg-[#718ebd] text-white py-1 px-4 rounded">
                        Добавить моба
                    </a>
                </div>

                
                <div class="p-4 bg-[#3b3a33] border-b border-[#a6925e]">
                    <form action="<?php echo e(route('admin.mobs.index')); ?>" method="GET"
                        class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        
                        <div>
                            <label for="search" class="block text-[#d9d3b8] font-semibold mb-1">Поиск по имени:</label>
                            <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                                class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]">
                        </div>

                        
                        <div>
                            <label for="location" class="block text-[#d9d3b8] font-semibold mb-1">Локация:</label>
                            <select name="location_id" id="location"
                                class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]">
                                <option value="">Все локации</option>
                                <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($location->id); ?>" <?php echo e($selectedLocationId == $location->id ? 'selected' : ''); ?>>
                                        <?php echo e($location->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <label for="min_level" class="block text-[#d9d3b8] font-semibold mb-1">Мин.
                                    уровень:</label>
                                <input type="number" name="min_level" id="min_level" value="<?php echo e(request('min_level')); ?>"
                                    min="1"
                                    class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]">
                            </div>
                            <div>
                                <label for="max_level" class="block text-[#d9d3b8] font-semibold mb-1">Макс.
                                    уровень:</label>
                                <input type="number" name="max_level" id="max_level" value="<?php echo e(request('max_level')); ?>"
                                    min="1"
                                    class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]">
                            </div>
                        </div>

                        
                        <div class="flex items-end space-x-2">
                            <button type="submit" class="bg-[#5e7ba6] hover:bg-[#718ebd] text-white py-2 px-4 rounded">
                                Применить
                            </button>
                            <a href="<?php echo e(route('admin.mobs.index')); ?>"
                                class="bg-[#a65e5e] hover:bg-[#b16b6b] text-white py-2 px-4 rounded">
                                Сбросить
                            </a>
                        </div>
                    </form>
                </div>

                
                <div class="p-4 overflow-x-auto">
                    <table class="w-full border-collapse">
                        <thead>
                            <tr class="bg-[#3b3a33] border-b-2 border-[#a6925e]">
                                <th class="p-2 text-left text-[#e5b769]">ID</th>
                                <th class="p-2 text-left text-[#e5b769]">Имя</th>
                                <th class="p-2 text-left text-[#e5b769]">Локация</th>
                                <th class="p-2 text-left text-[#e5b769]">Уровень</th>
                                <th class="p-2 text-left text-[#e5b769]">Здоровье</th>
                                <th class="p-2 text-left text-[#e5b769]">Опыт</th>
                                <th class="p-2 text-left text-[#e5b769]">Действия</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $mobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mob): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="border-b border-[#a6925e] hover:bg-[#3b3a33]">
                                    <td class="p-2 text-[#d9d3b8]"><?php echo e($mob->id); ?></td>
                                    <td class="p-2 text-[#d9d3b8]"><?php echo e($mob->name); ?></td>
                                    <td class="p-2 text-[#d9d3b8]"><?php echo e($mob->getEffectiveLocation() ?? 'Нет'); ?></td>
                                    <td class="p-2 text-[#d9d3b8]"><?php echo e($mob->level); ?></td>
                                    <td class="p-2 text-[#d9d3b8]"><?php echo e($mob->max_hp); ?></td>
                                    <td class="p-2 text-[#d9d3b8]"><?php echo e($mob->experience_reward); ?></td>
                                    <td class="p-2 flex flex-wrap gap-2">
                                        <a href="<?php echo e(route('admin.mobs.show', $mob)); ?>"
                                            class="bg-[#5e7ba6] hover:bg-[#718ebd] text-white py-1 px-2 rounded text-sm">
                                            Просмотр
                                        </a>
                                        <a href="<?php echo e(route('admin.mobs.edit', $mob)); ?>"
                                            class="bg-[#a6925e] hover:bg-[#e5b769] text-white py-1 px-2 rounded text-sm">
                                            Изменить
                                        </a>
                                        <form action="<?php echo e(route('admin.mobs.destroy', $mob)); ?>" method="POST" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit"
                                                onclick="return confirm('Вы уверены, что хотите удалить этого моба?')"
                                                class="bg-[#a65e5e] hover:bg-[#b16b6b] text-white py-1 px-2 rounded text-sm">
                                                Удалить
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="p-4 text-center text-[#d9d3b8]">Мобы не найдены</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                
                <div class="px-4 py-2">
                    <?php echo e($mobs->links()); ?>

                </div>

                
                <footer
                    class="bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] text-[#d9d3b8] py-1 mt-2 border-t-2 border-[#a6925e]">
                    <div class="w-full px-4 text-center">
                        <p class="text-sm sm:text-lg font-semibold">
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-[#32CD32] hover:text-[#f2ca76]">
                                Вернуться в админку
                            </a>
                        </p>
                    </div>
                </footer>
            </div>
        </main>
    </div>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/mobs/index.blade.php ENDPATH**/ ?>