<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Делаем поле chance nullable в таблице mob_skills
     * чтобы можно было использовать шанс из шаблона скилла
     */
    public function up(): void
    {
        Schema::table('mob_skills', function (Blueprint $table) {
            // Делаем поле chance nullable
            $table->integer('chance')->nullable()->change();
        });
    }

    /**
     * Откат миграции
     */
    public function down(): void
    {
        Schema::table('mob_skills', function (Blueprint $table) {
            // Возвращаем поле chance как NOT NULL
            $table->integer('chance')->nullable(false)->change();
        });
    }
};
