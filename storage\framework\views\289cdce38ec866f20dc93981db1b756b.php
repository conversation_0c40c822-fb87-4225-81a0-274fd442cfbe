<?php $__env->startSection('content'); ?>
    <!-- Статус фракций -->
    <div class="mt-2">
        <?php if (isset($component)) { $__componentOriginalb22308892d0e76a700f735dedd90d9f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb22308892d0e76a700f735dedd90d9f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.faction-status','data' => ['solWarriors' => $solWarriors ?? [],'solMages' => $solMages ?? [],'solKnights' => $solKnights ?? [],'lunWarriors' => $lunWarriors ?? [],'lunMages' => $lunMages ?? [],'lunKnights' => $lunKnights ?? [],'onlineCount' => $onlineCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.faction-status'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['solWarriors' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solWarriors ?? []),'solMages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solMages ?? []),'solKnights' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solKnights ?? []),'lunWarriors' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunWarriors ?? []),'lunMages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunMages ?? []),'lunKnights' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunKnights ?? []),'onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb22308892d0e76a700f735dedd90d9f6)): ?>
<?php $attributes = $__attributesOriginalb22308892d0e76a700f735dedd90d9f6; ?>
<?php unset($__attributesOriginalb22308892d0e76a700f735dedd90d9f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb22308892d0e76a700f735dedd90d9f6)): ?>
<?php $component = $__componentOriginalb22308892d0e76a700f735dedd90d9f6; ?>
<?php unset($__componentOriginalb22308892d0e76a700f735dedd90d9f6); ?>
<?php endif; ?>
    </div>

    
    <div class="mx-auto bg-[#2c2b25] text-white rounded-xl shadow-lg mb-4">
        <?php if (isset($component)) { $__componentOriginalaf57d894ddd69420481381dbd56ee5d9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaf57d894ddd69420481381dbd56ee5d9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.resource-block','data' => ['resourcesInLocation' => $resourcesInLocation ?? [],'user' => $user ?? null,'routePrefix' => 'battle.mines.custom']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.resource-block'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['resourcesInLocation' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($resourcesInLocation ?? []),'user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user ?? null),'routePrefix' => 'battle.mines.custom']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaf57d894ddd69420481381dbd56ee5d9)): ?>
<?php $attributes = $__attributesOriginalaf57d894ddd69420481381dbd56ee5d9; ?>
<?php unset($__attributesOriginalaf57d894ddd69420481381dbd56ee5d9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaf57d894ddd69420481381dbd56ee5d9)): ?>
<?php $component = $__componentOriginalaf57d894ddd69420481381dbd56ee5d9; ?>
<?php unset($__componentOriginalaf57d894ddd69420481381dbd56ee5d9); ?>
<?php endif; ?>
    </div>

    
    <div class="bg-[#2f2d2b] mb-3 mx-auto p-1 max-w-sm text-[#d9d3b8]">
        <?php if (isset($component)) { $__componentOriginald663c5c9d74e3851a752fc68bcac0a24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald663c5c9d74e3851a752fc68bcac0a24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.mob-list','data' => ['mobsInLocation' => $mobsInLocation ?? [],'user' => $user ?? null,'routePrefix' => 'battle.mines.custom']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.mob-list'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['mobsInLocation' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($mobsInLocation ?? []),'user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user ?? null),'routePrefix' => 'battle.mines.custom']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald663c5c9d74e3851a752fc68bcac0a24)): ?>
<?php $attributes = $__attributesOriginald663c5c9d74e3851a752fc68bcac0a24; ?>
<?php unset($__attributesOriginald663c5c9d74e3851a752fc68bcac0a24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald663c5c9d74e3851a752fc68bcac0a24)): ?>
<?php $component = $__componentOriginald663c5c9d74e3851a752fc68bcac0a24; ?>
<?php unset($__componentOriginald663c5c9d74e3851a752fc68bcac0a24); ?>
<?php endif; ?>
    </div>

    
    <?php if (isset($component)) { $__componentOriginal1e6b6234d72c4b96dd68762a784ee0d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1e6b6234d72c4b96dd68762a784ee0d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.target-actions','data' => ['user' => $user ?? null,'targetResource' => $targetResource ?? null,'targetMob' => $targetMob ?? null,'targetBot' => $targetBot ?? null,'target' => $target ?? null,'isStunned' => $isStunned ?? false,'lastAttacker' => $lastAttacker ?? null,'lastAttackerResources' => $lastAttackerResources ?? null,'routePrefix' => 'battle.mines.custom']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.target-actions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user ?? null),'targetResource' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($targetResource ?? null),'targetMob' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($targetMob ?? null),'targetBot' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($targetBot ?? null),'target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($target ?? null),'isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned ?? false),'lastAttacker' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lastAttacker ?? null),'lastAttackerResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lastAttackerResources ?? null),'routePrefix' => 'battle.mines.custom']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1e6b6234d72c4b96dd68762a784ee0d7)): ?>
<?php $attributes = $__attributesOriginal1e6b6234d72c4b96dd68762a784ee0d7; ?>
<?php unset($__attributesOriginal1e6b6234d72c4b96dd68762a784ee0d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1e6b6234d72c4b96dd68762a784ee0d7)): ?>
<?php $component = $__componentOriginal1e6b6234d72c4b96dd68762a784ee0d7; ?>
<?php unset($__componentOriginal1e6b6234d72c4b96dd68762a784ee0d7); ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.mine', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/battle/mines/locations/mom_rud.blade.php ENDPATH**/ ?>