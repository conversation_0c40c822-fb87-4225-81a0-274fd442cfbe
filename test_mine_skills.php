<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ТЕСТ СИСТЕМЫ СКИЛЛОВ В РУДНИКАХ ===\n\n";

// Получаем тестового игрока
$user = \App\Models\User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

// Получаем моба с скиллами в руднике
$mob = \App\Models\Mob::whereNotNull('mine_location_id')
    ->whereHas('skills')
    ->with(['skills.skillTemplate'])
    ->first();

if (!$mob) {
    echo "❌ Не найден моб с скиллами в руднике!\n";
    exit(1);
}

echo "🎯 Тестируем моба: {$mob->name} (ID: {$mob->id})\n";
echo "   Локация рудника: {$mob->mine_location_id}\n";
echo "   HP: {$mob->hp}/{$mob->max_hp}\n";
echo "   Скиллов: " . $mob->skills->count() . "\n\n";

// Показываем скиллы моба
echo "📋 СКИЛЛЫ МОБА:\n";
foreach ($mob->skills as $mobSkill) {
    $template = $mobSkill->skillTemplate;
    echo "   - {$template->name}\n";
    echo "     Шанс моба: " . ($mobSkill->chance ?? 'не задан') . "%\n";
    echo "     Шанс шаблона: {$template->chance}%\n";
    echo "     Кулдаун: {$template->cooldown}с\n";
    echo "     Доступен: " . ($mobSkill->isAvailable() ? 'Да' : 'Нет') . "\n";
    if ($mobSkill->cooldown_ends_at) {
        echo "     Кулдаун до: {$mobSkill->cooldown_ends_at}\n";
    }
    if ($mobSkill->last_used_at) {
        echo "     Последнее использование: {$mobSkill->last_used_at}\n";
    }
    echo "\n";
}

// Тестируем MobSkillFramework
echo "🧪 ТЕСТИРУЕМ MOBSKILLFRAMEWORK:\n";
$mobSkillFramework = app(\App\Services\MobSkillFramework::class);

// Создаем контекст для атаки
$context = [
    'event_type' => 'attack',
    'mine_location_id' => $mob->mine_location_id,
    'target_type' => 'player',
    'is_mine_mob' => true,
    'damage_dealt' => 10,
    'attack_type' => 'test_attack'
];

echo "   Контекст: " . json_encode($context, JSON_UNESCAPED_UNICODE) . "\n\n";

// Выполняем тест активации скиллов
echo "🎲 АКТИВАЦИЯ СКИЛЛОВ:\n";
for ($i = 1; $i <= 5; $i++) {
    echo "   Попытка #{$i}:\n";

    $activatedSkills = $mobSkillFramework->processMobSkills($mob, $user, $context);

    if (empty($activatedSkills)) {
        echo "     ❌ Скиллы не активированы\n";
    } else {
        echo "     ✅ Активировано скиллов: " . count($activatedSkills) . "\n";
        echo "     Данные скиллов: " . json_encode($activatedSkills, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        foreach ($activatedSkills as $skill) {
            $skillName = $skill['skill_name'] ?? $skill['name'] ?? 'Неизвестный скилл';
            $skillType = $skill['skill_type'] ?? $skill['type'] ?? 'unknown';
            echo "       - {$skillName} (тип: {$skillType})\n";
            if (isset($skill['damage']) && $skill['damage'] > 0) {
                echo "         Урон: {$skill['damage']}\n";
            }
            if (isset($skill['message'])) {
                echo "         Сообщение: {$skill['message']}\n";
            }
        }
    }

    // Проверяем состояние кулдаунов после активации
    $mob->refresh();
    $mob->load('skills.skillTemplate');

    echo "     Состояние кулдаунов:\n";
    foreach ($mob->skills as $mobSkill) {
        $template = $mobSkill->skillTemplate;
        $available = $mobSkill->isAvailable();
        $remaining = $mobSkill->getRemainingCooldown();

        echo "       * {$template->name}: " . ($available ? 'Доступен' : "Кулдаун {$remaining}с") . "\n";
    }

    echo "\n";

    // Ждем немного между попытками
    if ($i < 5) {
        sleep(2);
    }
}

echo "=== ТЕСТ ЗАВЕРШЕН ===\n";
