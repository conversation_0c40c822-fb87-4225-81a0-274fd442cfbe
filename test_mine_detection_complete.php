<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ПОЛНЫЙ ТЕСТ СИСТЕМЫ ОБНАРУЖЕНИЯ В РУДНИКАХ ===\n\n";

// Получаем тестового игрока
$user = \App\Models\User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

// Получаем локацию рудника
$mineLocation = \App\Models\MineLocation::with('spawnedResources')->first();
if (!$mineLocation) {
    echo "❌ Локация рудника не найдена!\n";
    exit(1);
}

echo "🏔️ Тестируем рудник: {$mineLocation->name}\n";
echo "   ID: {$mineLocation->id}\n";
echo "   Ресурсов: " . $mineLocation->spawnedResources->count() . "\n\n";

// Очищаем старые метки и эффекты
echo "🧹 ОЧИСТКА СТАРЫХ ДАННЫХ:\n";
\App\Models\MineMark::where('player_id', $user->id)->delete();
\App\Models\ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();
echo "   ✅ Старые метки и эффекты удалены\n\n";

// Тестируем MineDetectionService
echo "🎯 ТЕСТИРУЕМ MINEDETECTIONSERVICE:\n";
$mineDetectionService = app(\App\Services\MineDetectionService::class);

// Применяем дебаф обнаружения
echo "   Применяем дебаф обнаружения...\n";
$mark = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);

if ($mark) {
    echo "   ✅ Метка создана (ID: {$mark->id})\n";
    echo "   Игрок: {$mark->player_id}\n";
    echo "   Рудник: {$mark->mine_location_id}\n";
    echo "   Истекает: {$mark->expires_at}\n";
    echo "   Активна: " . ($mark->is_active ? 'Да' : 'Нет') . "\n\n";
} else {
    echo "   ❌ Метка НЕ создана!\n\n";
    exit(1);
}

// Проверяем создание ActiveEffect
echo "🔍 ПРОВЕРЯЕМ ACTIVEEFFECT:\n";
$activeEffect = \App\Models\ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->first();

if ($activeEffect) {
    echo "   ✅ ActiveEffect создан (ID: {$activeEffect->id})\n";
    echo "   Тип: {$activeEffect->effect_type}\n";
    echo "   Заканчивается: {$activeEffect->ends_at}\n";
    echo "   Активен: " . ($activeEffect->ends_at > now() ? 'Да' : 'Нет') . "\n\n";
} else {
    echo "   ❌ ActiveEffect НЕ создан!\n\n";
}

// Проверяем получение обнаруженных игроков
echo "👥 ПРОВЕРЯЕМ ПОЛУЧЕНИЕ ОБНАРУЖЕННЫХ ИГРОКОВ:\n";
$detectedPlayers = $mineDetectionService->getDetectedPlayers(
    $mineLocation->location_id,
    $mineLocation->id
);

echo "   Найдено обнаруженных игроков: " . count($detectedPlayers) . "\n";
foreach ($detectedPlayers as $playerData) {
    echo "   - Игрок ID: " . ($playerData['player_id'] ?? 'Неизвестно') . "\n";
    echo "     Имя: " . ($playerData['player_name'] ?? 'Неизвестно') . "\n";
    echo "     Истекает: " . ($playerData['expires_at'] ?? 'Неизвестно') . "\n";
    echo "     Атак получено: " . ($playerData['attack_count'] ?? 0) . "\n";
}
echo "\n";

// Тестируем атаку мобов на обнаруженного игрока
echo "⚔️ ТЕСТИРУЕМ АТАКУ МОБОВ:\n";
$mineMobs = \App\Models\Mob::where('mine_location_id', $mineLocation->id)
    ->with('skills.skillTemplate')
    ->get();

if ($mineMobs->count() > 0) {
    echo "   Найдено мобов в руднике: " . $mineMobs->count() . "\n";

    foreach ($mineMobs as $mob) {
        echo "   \n   🐉 Моб: {$mob->name} (ID: {$mob->id})\n";
        echo "      HP: {$mob->hp}/{$mob->max_hp}\n";
        echo "      Скиллов: " . $mob->skills->count() . "\n";

        if ($mob->skills->count() > 0) {
            // Тестируем MobSkillIntegrationService
            $mobSkillService = app(\App\Services\Mine\MobSkillIntegrationService::class);

            $context = [
                'mine_location_id' => $mineLocation->id,
                'location_id' => $mineLocation->location_id,
                'damage_dealt' => 10,
                'attack_type' => 'mine_detection_test'
            ];

            echo "      Тестируем атаку на обнаруженного игрока...\n";
            $activatedSkills = $mobSkillService->processMobAttackSkills($mob, $user, $context);

            if (empty($activatedSkills)) {
                echo "      ❌ Скиллы не активированы\n";
            } else {
                echo "      ✅ Активировано скиллов: " . count($activatedSkills) . "\n";
                foreach ($activatedSkills as $skill) {
                    $skillName = $skill['skill_name'] ?? $skill['name'] ?? 'Неизвестный скилл';
                    echo "        - {$skillName}\n";
                    if (isset($skill['message'])) {
                        echo "          {$skill['message']}\n";
                    }
                }
            }
        }
    }
} else {
    echo "   ❌ Мобы в руднике не найдены!\n";
}

echo "\n";

// Проверяем финальное состояние
echo "📊 ФИНАЛЬНОЕ СОСТОЯНИЕ:\n";
$finalMark = \App\Models\MineMark::where('player_id', $user->id)
    ->where('mine_location_id', $mineLocation->id)
    ->first();

if ($finalMark) {
    echo "   ✅ Метка активна\n";
    echo "   Атак получено: {$finalMark->attack_count}\n";
    echo "   Последняя атака: " . ($finalMark->last_attack_at ?? 'Нет') . "\n";
} else {
    echo "   ❌ Метка не найдена\n";
}

$finalEffect = \App\Models\ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->first();

if ($finalEffect) {
    echo "   ✅ Эффект активен до: {$finalEffect->ends_at}\n";
} else {
    echo "   ❌ Эффект не найден\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
